<?php

declare(strict_types=1);

use App\Actions\CommissionClosing\GenerateCommissionClosingPdf;
use App\Actions\Order\GenerateOrderPdf;
use App\Actions\Order\GenerateOrdersPdf;
use App\Actions\Reports\GenerateOrdersReport;
use App\Filament\Pages\CreateCustomerProductVariantItemUnitsOrder;
use App\Filament\Pages\CreateCustomItemOrder;
use App\Filament\Pages\CustomerAbcCurveDetails;
use App\Filament\Pages\EditCustomerProductVariantItemUnitsOrder;
use App\Filament\Pages\EditOrderPrintLayouts;
use App\Filament\Pages\EditTenantSettings;
use App\Filament\Pages\Report\LoadOrdersReport;
use App\Filament\Pages\UpdatePassword;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->group(function () {
    Route::get('/', function () {
        return redirect()->route('filament.app.auth.login');
    });

    Route::get('orders/create-customer-product-variant-item-units-order', CreateCustomerProductVariantItemUnitsOrder::class)->name('filament.app.pages.create-customer-product-variant-item-units-order');
    Route::get('orders/create-custom-item-order', CreateCustomItemOrder::class)->name('filament.app.pages.create-custom-item-order');
    Route::get('orders/edit-customer-product-variant-item-units-order/{order}', EditCustomerProductVariantItemUnitsOrder::class)->name('filament.app.pages.edit-customer-product-variant-item-units-order');

    Route::prefix('app')->group(function () {
        Route::get('change-password', UpdatePassword::class)->name('filament.pages.change-password');
        Route::get('edit-tenant-settings', EditTenantSettings::class)->name('filament.app.pages.edit-tenant-settings');
        Route::get('edit-order-print-layouts', EditOrderPrintLayouts::class)->name('filament.app.pages.edit-order-print-layouts');

        Route::prefix('indicators')->group(function () {
            Route::get('customer-abc-curve', CustomerAbcCurveDetails::class)->name('filament.app.indicators.customer-abc-curve-details');
        });

        Route::get('orders/{record}/{type}/pdf', GenerateOrderPdf::class)->name('orders.generate-pdf');
        Route::get('orders/{recordsToken}/{type}/bulk-pdf', GenerateOrdersPdf::class)->name('orders.generate-bulk-pdf');

        Route::get('commission-closings/{commissionClosing}/pdf', GenerateCommissionClosingPdf::class)->name('commission_closings.generate-pdf');

        Route::get('reports/load-orders-report', LoadOrdersReport::class)->name('reports.load_general_orders_report');
        Route::get('reports/orders-report', GenerateOrdersReport::class)->name('reports.generate_general_orders_report');
    });
});
