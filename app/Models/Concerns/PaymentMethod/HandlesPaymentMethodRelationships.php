<?php

namespace App\Models\Concerns\PaymentMethod;

use App\Models\Customer;
use App\Models\Order;
use App\Models\ThirdPartyPaymentMethod;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesPaymentMethodRelationships
{
    public function customers(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Customer::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function thirdPartyPaymentMethods(): HasMany
    {
        return $this->hasMany(ThirdPartyPaymentMethod::class);
    }
}
