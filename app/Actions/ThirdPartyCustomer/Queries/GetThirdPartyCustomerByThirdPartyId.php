<?php

namespace App\Actions\ThirdPartyCustomer\Queries;

use App\Models\ThirdPartyCustomer;
use Lorisleiva\Actions\Concerns\AsAction;

class GetThirdPartyCustomerByThirdPartyId
{
    use AsAction;

    public function handle(int $integrationTypeId, int $thirdPartyId): ?ThirdPartyCustomer
    {
        return ThirdPartyCustomer::query()
            ->where('integration_type_id', $integrationTypeId)
            ->where('third_party_id', $thirdPartyId)
            ->first();
    }
}
