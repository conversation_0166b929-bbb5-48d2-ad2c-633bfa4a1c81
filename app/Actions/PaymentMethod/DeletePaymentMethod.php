<?php

namespace App\Actions\PaymentMethod;

use App\Models\PaymentMethod;
use Exception;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeletePaymentMethod
{
    use AsAction;

    public function handle(PaymentMethod $paymentMethod): void
    {
        if ($paymentMethod->customers->isNotEmpty()) {
            throw new Exception(__('payment_methods.responses.delete.errors.existing_customers'));
        }

        if ($paymentMethod->orders->isNotEmpty()) {
            throw new Exception(__('payment_methods.responses.delete.errors.existing_orders'));
        }

        try {
            DB::transaction(function () use ($paymentMethod): void {
                $paymentMethod->thirdPartyPaymentMethods()->delete();
                $paymentMethod->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
