<?php

namespace App\Actions\Haulier;

use App\Models\Haulier;
use Exception;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteHaulier
{
    use AsAction;

    public function handle(Haulier $haulier): void
    {
        if ($haulier->customers->isNotEmpty()) {
            throw new Exception(__('hauliers.responses.delete.errors.existing_customers'));
        }

        if ($haulier->orders->isNotEmpty()) {
            throw new Exception(__('hauliers.responses.delete.errors.existing_orders'));
        }

        try {
            DB::transaction(function () use ($haulier): void {
                $haulier->thirdPartyHauliers()->delete();
                $haulier->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
