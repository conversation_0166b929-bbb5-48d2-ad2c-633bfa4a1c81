<?php

namespace App\Filament\Widgets;

use App\Enums\RoleEnum;
use App\Models\Order;
use Filament\Forms\Components\DatePicker;
use Filament\Support\RawJs;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class CustomerAbcCurveChart extends ApexChartWidget
{
    protected int | string | array $columnSpan = [
        'xs' => 12,
        'sm' => 12,
        'md' => 12,
        'lg' => 6,
        'xl' => 6,
    ];

    protected static ?int $sort = 4;

    protected static ?string $chartId = 'customerAbcCurveChart';

    protected static ?string $heading = 'Curva ABC de clientes';

    public static function canView(): bool
    {
        return auth()->user()->hasRole(RoleEnum::Administrator->value);
    }

    protected function getOptions(): array
    {
        $start = carbon($this->filterFormData['Vendas de']);
        $end = carbon($this->filterFormData['Vendas até']);

        $data = Order::query()
            ->select([
                'customer_id',
                DB::raw('sum(total_amount) as amount_per_period')
            ])
            ->where('issued_at', '>=', $start->format('Y-m-d'))
            ->where('issued_at', '<=', $end->format('Y-m-d'))
            ->orderByDesc(DB::raw('sum(total_amount)'))
            ->groupBy('customer_id')
            ->get()
            ->mapWithKeys(function (Order $order): array {
                return [
                    $order->customer_id => $order->amount_per_period
                ];
            });

        $customerCount = $data->count();
        $categoryA = $data->take((int)($customerCount * 0.2));
        $categoryB = $data->slice((int)($customerCount * 0.2), (int)($customerCount * 0.3));
        $categoryC = $data->slice((int)($customerCount * 0.5));

        $revenueA = $categoryA->sum();
        $revenueB = $categoryB->sum();
        $revenueC = $categoryC->sum();

        return [
            'chart' => [
                'type' => 'donut',
                'height' => 245,
                'toolbar' => [
                    'show' => false,
                ]
            ],
            'series' => [
                $revenueA,
                $revenueB,
                $revenueC,
            ],
            'labels' => [
                'Curva A',
                'Curva B',
                'Curva C',
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: {
                labels: {
                    formatter: function (value) {
                        return value.toLocaleString('pt-BR',{ style: 'currency', currency: 'BRL' });
                    }
                }
            }
        }
        JS);
    }

    protected function getFormSchema(): array
    {
        return [
            DatePicker::make('Vendas de')
                ->default(now()->setTimezone('-3:00')->subDays(7)->format('Y-m-d')),
            DatePicker::make('Vendas até')
                ->default(now()->setTimezone('-3:00')->format('Y-m-d')),
        ];
    }

    protected function getFooter(): null|string|View
    {
        $html = '<span class="flex justify-center text-sm border-t py-4"><a href="' . route('filament.app.indicators.customer-abc-curve-details') . '">Detalhamento da curva ABC</a></span>';

        return new HtmlString($html);
    }
}
