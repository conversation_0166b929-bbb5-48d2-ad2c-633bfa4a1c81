<?php

namespace App\Filament\Widgets;

use App\Enums\OrderStatusEnum;
use App\Enums\RoleEnum;
use App\Models\Order;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class LatestOrdersTableWidget extends BaseWidget
{
    protected int | string | array $columnSpan = 12;

    protected static ?int $sort = 5;

    /** @inheritDoc */
    public static function canView(): bool
    {
        return auth()->user()->hasRole(RoleEnum::Administrator->value);
    }

    /** @inheritDoc */
    public function table(Table $table): Table
    {
        return $table
            ->heading('Últimos pedidos')
            ->paginated(false)
            ->query(
                Order::query()
                    ->take(5)
                    ->orderByDesc('id')
            )
            ->columns([
                TextColumn::make('id')
                    ->label(__('orders.forms.fields.id')),
                TextColumn::make('customer.name')
                    ->label(__('customers.forms.fields.name')),
                TextColumn::make('customer.trading_name')
                    ->label(__('customers.forms.fields.trading_name')),
                TextColumn::make('issued_at')
                    ->label(__('orders.forms.fields.issued_at'))
                    ->date('d/m/Y'),
                TextColumn::make('total_amount')
                    ->label(__('orders.forms.fields.total_amount'))
                    ->formatStateUsing(fn(Order $order): string => $order->friendly_total_amount),
                TextColumn::make('creationUser.name')
                    ->label(__('orders.forms.fields.created_by_user_id'))
                    ->visible(auth()->user()->hasRole(RoleEnum::Administrator->value)),
                TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(fn(Order $order): string => OrderStatusEnum::getTranslated()[$order->status])
                    ->color(fn(Order $order): string => OrderStatusEnum::getTableColors()[$order->status])
            ]);
    }
}
