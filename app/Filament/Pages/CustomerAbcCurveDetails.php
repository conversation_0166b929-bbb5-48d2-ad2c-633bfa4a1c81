<?php

namespace App\Filament\Pages;

use App\Enums\RoleEnum;
use Filament\Actions\Action;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Pages\Page;

class CustomerAbcCurveDetails extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $title = 'Curva ABC de clientes';
    protected static ?string $slug = 'customer-abc-curve-details';
    protected static string $view = 'filament.pages.customer-abc-curve-details';

    public ?string $date_from;
    public ?string $date_to;

    public static function canView(): bool
    {
        return auth()->user()->hasRole(RoleEnum::Administrator->value);
    }

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Voltar')
                ->color('gray')
                ->url(route('filament.app.pages.dashboard')),
        ];
    }

    public function mount(): void
    {
        $this->date_from = now()->startOfMonth()->format('Y-m-d');
        $this->date_to = now()->endOfMonth()->format('Y-m-d');
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(4)->schema([
                DatePicker::make('date_from')
                    ->label('Vendas de'),
                DatePicker::make('date_to')
                    ->label('Vendas até'),
            ]),
            Grid::make(1)->schema([
                Tabs::make()->schema([
                    Tab::make('A')->schema([
                        //
                    ]),
                    Tab::make('B')->schema([
                        //
                    ]),
                    Tab::make('C')->schema([
                        //
                    ]),
                ]),
            ]),
        ]);
    }
}
