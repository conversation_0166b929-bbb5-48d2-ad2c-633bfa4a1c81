<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\ErpFlex\Requests\Sql\ErpFlexRunSqlQueryRequest;

class ErpFlexCustomerSalesmenService extends ErpFlexBaseService
{
    public function getByCustomerId(int $customerId): array
    {
        $sql = 'SELECT * FROM SA1_SA3 WHERE SA1_SA3_IDSA1 = ' . $customerId;

        $request = new ErpFlexRunSqlQueryRequest($sql);
        $response = $this->connector->send($request);

        return $this->getQueryResultsFromApi(
            json_decode($response->body())
        );
    }
}
